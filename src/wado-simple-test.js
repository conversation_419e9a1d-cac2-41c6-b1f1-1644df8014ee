import http from 'k6/http';
import { sleep, check } from 'k6';

// Konfigurasi test sederhana
export const options = {
  vus: 10, // 10 virtual users
  duration: '2m', // Durasi 2 menit
  thresholds: {
    http_req_duration: ['p(95)<3000'], // 95% request < 3 detik
    http_req_failed: ['rate<0.1'], // Error rate < 10%
  },
};

// URL WADO yang akan di-test
const WADO_URL = 'http://192.168.7.167:8080/wado?requestType=WADO&studyUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.780&seriesUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.786&objectUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.820.1&columns=128';

export default function() {
  // Kirim GET request
  const response = http.get(WADO_URL, {
    headers: {
      'Accept': 'application/dicom, image/jpeg, image/png, */*',
    },
    timeout: '10s',
  });
  
  // Validasi response
  check(response, {
    'Status 200': (r) => r.status === 200,
    'Response time OK': (r) => r.timings.duration < 5000,
    'Ada content': (r) => r.body && r.body.length > 0,
  });
  
  // Jeda 1 detik
  sleep(1);
}

export function setup() {
  console.log('🧪 WADO Simple Test dimulai');
  console.log(`📍 URL: ${WADO_URL}`);
  console.log('👥 10 users selama 2 menit');
}
