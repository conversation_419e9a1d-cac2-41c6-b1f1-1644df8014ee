import http from 'k6/http';
import { sleep, check } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics untuk monitoring
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// Konfigurasi stress test
export const options = {
  stages: [
    // Ramp up: <PERSON>ik bertahap ke 50 users dalam 2 menit
    { duration: '2m', target: 50 },
    // Stay: Maintain 50 users selama 5 menit
    { duration: '5m', target: 50 },
    // Peak: Naik ke 100 users dalam 2 menit
    { duration: '2m', target: 100 },
    // Peak load: Maintain 100 users selama 5 menit
    { duration: '5m', target: 100 },
    // Ramp down: Turun ke 0 dalam 2 menit
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    // 95% request harus selesai dalam 2 detik
    http_req_duration: ['p(95)<2000'],
    // Error rate harus di bawah 1%
    errors: ['rate<0.01'],
    // 95% request harus berhasil
    http_req_failed: ['rate<0.05'],
  },
};

// URL WADO yang akan di-test
const WADO_URL = 'http://192.168.7.167:8080/wado?requestType=WADO&studyUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.780&seriesUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.786&objectUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.820.1&columns=128';

export default function() {
  // Catat waktu mulai request
  const startTime = Date.now();
  
  // Kirim GET request ke WADO endpoint
  const response = http.get(WADO_URL, {
    headers: {
      'Accept': 'application/dicom, image/jpeg, image/png, */*',
      'User-Agent': 'K6-WADO-StressTest/1.0',
    },
    timeout: '30s', // Timeout 30 detik
  });
  
  // Hitung response time
  const duration = Date.now() - startTime;
  responseTime.add(duration);
  
  // Validasi response
  const isSuccess = check(response, {
    'Status adalah 200': (r) => r.status === 200,
    'Response time < 5000ms': (r) => r.timings.duration < 5000,
    'Response memiliki content': (r) => r.body && r.body.length > 0,
    'Content-Type header ada': (r) => r.headers['Content-Type'] !== undefined,
  });
  
  // Catat error jika ada
  errorRate.add(!isSuccess);
  
  // Log untuk debugging (hanya untuk beberapa request)
  if (__VU === 1 && __ITER < 3) {
    console.log(`VU: ${__VU}, Iteration: ${__ITER}`);
    console.log(`Status: ${response.status}`);
    console.log(`Response Time: ${response.timings.duration}ms`);
    console.log(`Content-Type: ${response.headers['Content-Type']}`);
    console.log(`Body Size: ${response.body ? response.body.length : 0} bytes`);
    console.log('---');
  }
  
  // Jeda antar request (1-3 detik secara random)
  sleep(Math.random() * 2 + 1);
}

// Setup function - dijalankan sekali di awal
export function setup() {
  console.log('🚀 Memulai WADO Stress Test');
  console.log(`📍 Target URL: ${WADO_URL}`);
  console.log('📊 Test akan berjalan dengan tahapan:');
  console.log('   - Ramp up: 0→50 users (2 menit)');
  console.log('   - Maintain: 50 users (5 menit)');
  console.log('   - Peak: 50→100 users (2 menit)');
  console.log('   - Peak load: 100 users (5 menit)');
  console.log('   - Ramp down: 100→0 users (2 menit)');
  console.log('⏱️  Total durasi: 16 menit');
  console.log('');
}

// Teardown function - dijalankan sekali di akhir
export function teardown(data) {
  console.log('');
  console.log('✅ WADO Stress Test selesai');
  console.log('📈 Lihat hasil lengkap di summary report di atas');
}
