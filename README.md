# WADO Performance Test

Proyek ini berisi stress test untuk endpoint WADO (Web Access to DICOM Objects) menggunakan K6.

## URL Target
```
http://192.168.7.167:8080/wado?requestType=WADO&studyUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.780&seriesUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.786&objectUID=1.2.840.113619.2.428.3.2831170560.627.1632182294.820.1&columns=128
```

## Instalasi

1. Pastikan K6 sudah terinstall di sistem:
   ```bash
   # Windows (menggunakan Chocolatey)
   choco install k6
   
   # Atau download dari https://k6.io/docs/get-started/installation/
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

## Cara Menjalankan Test

### 1. Simple Test (Ringan)
Test sederhana dengan 10 users selama 2 menit:
```bash
npm run wado-simple
```

### 2. Stress Test (Lengkap)
Test lengkap dengan tahapan load yang bertingkat:
```bash
npm run wado-stress
```

### 3. Stress Test dengan HTML Report
Menghasilkan laporan HTML yang detail:
```bash
npm run wado-stress-html
```

### 4. Test Manual dengan K6
```bash
# Simple test
k6 run src/wado-simple-test.js

# Stress test
k6 run src/wado-stress-test.js

# Dengan custom options
k6 run --vus 20 --duration 5m src/wado-simple-test.js
```

## Skenario Test

### Simple Test (`wado-simple-test.js`)
- **Virtual Users**: 10
- **Durasi**: 2 menit
- **Target**: Test dasar untuk memastikan endpoint berfungsi

### Stress Test (`wado-stress-test.js`)
- **Tahap 1**: Ramp up 0→50 users (2 menit)
- **Tahap 2**: Maintain 50 users (5 menit)
- **Tahap 3**: Peak 50→100 users (2 menit)
- **Tahap 4**: Peak load 100 users (5 menit)
- **Tahap 5**: Ramp down 100→0 users (2 menit)
- **Total Durasi**: 16 menit

## Metrics yang Dimonitor

- **Response Time**: Waktu respons per request
- **Throughput**: Jumlah request per detik
- **Error Rate**: Persentase request yang gagal
- **Status Code**: Validasi HTTP status
- **Content Validation**: Memastikan response memiliki content

## Threshold (Batas Keberhasilan)

### Simple Test
- 95% request harus selesai dalam 3 detik
- Error rate harus di bawah 10%

### Stress Test
- 95% request harus selesai dalam 2 detik
- Error rate harus di bawah 1%
- Request failure rate harus di bawah 5%

## Interpretasi Hasil

### Metrics Penting:
- **http_req_duration**: Waktu respons (semakin rendah semakin baik)
- **http_req_failed**: Persentase request gagal (harus mendekati 0%)
- **http_reqs**: Total request yang berhasil dikirim
- **vus**: Jumlah virtual users aktif

### Contoh Output Sukses:
```
✓ Status adalah 200
✓ Response time < 5000ms
✓ Response memiliki content
✓ Content-Type header ada
```

## Troubleshooting

### Jika Test Gagal:
1. **Connection Error**: Pastikan server WADO berjalan di `192.168.7.167:8080`
2. **Timeout**: Coba kurangi jumlah virtual users
3. **High Error Rate**: Periksa kapasitas server atau network

### Menyesuaikan Test:
- Edit file `src/wado-stress-test.js` untuk mengubah skenario
- Ubah `options.stages` untuk menyesuaikan load pattern
- Modifikasi `thresholds` untuk mengubah kriteria sukses

## File Struktur
```
├── src/
│   ├── wado-stress-test.js    # Stress test lengkap
│   ├── wado-simple-test.js    # Test sederhana
│   └── ping.js                # Test ping dasar
├── package.json               # Dependencies dan scripts
└── README.md                  # Dokumentasi ini
```
